<?php
/**
 * WooCommerce Template Functions.
 *
 * @package dmrthema
 */

if ( ! function_exists( 'dmrthema_woo_cart_available' ) ) {
	/**
	 * Validates whether the Woo Cart instance is available in the request
	 *
	 * @since 1.0.0
	 * @return bool
	 */
	function dmrthema_woo_cart_available() {
		$woo = WC();
		return $woo instanceof \WooCommerce && $woo->cart instanceof \WC_Cart;
	}
}

if ( ! function_exists( 'dmrthema_before_content' ) ) {
	/**
	 * Before Content
	 * Wraps all WooCommerce content in wrappers which match the theme markup
	 *
	 * @since   1.0.0
	 * @return  void
	 */
	function dmrthema_before_content() {
		?>
		<div id="primary" class="content-area">
			<main id="main" class="site-main" role="main">
		<?php
	}
}

if ( ! function_exists( 'dmrthema_after_content' ) ) {
	/**
	 * After Content
	 * Closes the wrapping divs
	 *
	 * @since   1.0.0
	 * @return  void
	 */
	function dmrthema_after_content() {
		?>
			</main><!-- #main -->
		</div><!-- #primary -->
		<?php
	}
}

/**
 * Product Gallery Image for Loop
 */
function dmrthema_gallery_image() {
	global $product;
	$attachment_ids = $product->get_gallery_image_ids();

	$dmrthema_layout_woocommerce_flip_image = '';
	$dmrthema_layout_woocommerce_flip_image = get_theme_mod( 'dmrthema_layout_woocommerce_flip_image', true );

	if ( true === $dmrthema_layout_woocommerce_flip_image ) {
		if ( isset( $attachment_ids[0] ) ) {
			echo wp_kses_post( wp_get_attachment_image( ( $attachment_ids[0] ), 'woocommerce_thumbnail', '', array( 'loading' => 'lazy', 'class' => 'gallery-image' ) ) );
		}
	}
}

/**
 * Product loop image wrapper open
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_loop_product_image_wrapper_open', 4 );
function dmrthema_loop_product_image_wrapper_open() {
	echo '<div class="woocommerce-image__wrapper">';
}

/**
 * Product loop image wrapper close
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_loop_product_image_wrapper_close', 60 );
function dmrthema_loop_product_image_wrapper_close() {
	echo '</div>';
}

/**
 * Single Product Page - Add a section wrapper start.
 */
add_action( 'woocommerce_before_single_product_summary', 'dmrthema_product_content_wrapper_start', 5 );
function dmrthema_product_content_wrapper_start() {
	echo '<div class="product-details-wrapper">';
}

/**
 * Single Product Page - Add a section wrapper end.
 */
add_action( 'woocommerce_single_product_summary', 'dmrthema_product_content_wrapper_end', 120 );
function dmrthema_product_content_wrapper_end() {
	echo '</div><!--/product-details-wrapper-end-->';
}

/**
 * Single Product - Display custom content below Buy Now Button
 */
add_action( 'woocommerce_single_product_summary', 'dmrthema_product_custom_content', 45 );

/**
 * Custom markup around single product field - if in stock.
 */
function dmrthema_product_custom_content() {
	$dmrthema_disable_pdp_custom_widget = get_post_meta( get_the_ID(), 'dmrthema-disable-pdp-custom-widget', true );

	if ( 'disabled' !== $dmrthema_disable_pdp_custom_widget ) {
		if ( is_active_sidebar( 'single-product-field' ) ) :
			echo '<div class="product-widget">';
			dynamic_sidebar( 'single-product-field' );
			echo '</div>';
		endif;
	}
}

if ( class_exists( 'WooCommerce' ) ) {
	add_action( 'get_header', 'dmrthema_remove_product_sidebar' );

	/**
	 * Remove sidebar on a single product page.
	 */
	function dmrthema_remove_product_sidebar() {
		if ( is_product() ) {
			remove_action( 'dmrthema_sidebar', 'dmrthema_get_sidebar', 10 );
		}
	}
}

/**
 * Single Product Page - Reorder sale message.
 */
remove_action( 'woocommerce_before_single_product_summary', 'woocommerce_show_product_sale_flash', 10 );
add_action( 'woocommerce_single_product_summary', 'woocommerce_show_product_sale_flash', 3 );

/**
 * Single Product Page - Change gallery thumbnails to one column.
 */
function dmrthema_gallery_columns() {
	return 1;
}
add_filter( 'dmrthema_product_thumbnail_columns', 'dmrthema_gallery_columns' );

/**
 * Single Product Page - Include navigation arrows within the core PDP slider.
 */
function dmrthema_flexslider_options( $options ) {
	$options['directionNav'] = true;
	return $options;
}
add_filter( 'woocommerce_single_product_carousel_options', 'dmrthema_flexslider_options' );

/**
 * Single Product Page - Reorder Rating position.
 */
remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 20 );

/**
 * Add a div with an ID before the variations form, so that the sticky select options button can scroll up to it.
 */
add_action( 'woocommerce_before_add_to_cart_form', 'dmrthema_sticky_variations_anchor' );

function dmrthema_sticky_variations_anchor() {
	echo '<div id="dmrthema-sticky-anchor"></div>';
}

/**
 * PDP Modal wrapper - open.
 */
add_action( 'woocommerce_single_product_summary', 'dmrthema_pdp_modal_wrapper_open', 36 );

function dmrthema_pdp_modal_wrapper_open() {
	echo '<div id="dmrthema-modals-wrapper">';
}

/**
 * PDP Modal wrapper - close.
 */
add_action( 'woocommerce_single_product_summary', 'dmrthema_pdp_modal_wrapper_close', 38 );

function dmrthema_pdp_modal_wrapper_close() {
	echo '</div>';
}

/**
 * Single Product Page - Reorder Rating position.
 */
remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10 );
add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_rating', 20 );

/**
 * Shop page - Out of Stock
 */
if ( ! function_exists( 'dmrthema_shop_out_of_stock' ) ) :
	/**
	 * Add Out of Stock to the Shop page
	 *
	 * @hooked woocommerce_before_shop_loop_item_title - 8
	 *
	 * @since 1.0.0
	 */
	function dmrthema_shop_out_of_stock() {
		$out_of_stock        = get_post_meta( get_the_ID(), '_stock_status', true );
		$out_of_stock_string = apply_filters( 'dmrthema_shop_out_of_stock_string', __( 'Out of stock', 'dmrthema' ) );
		if ( 'outofstock' === $out_of_stock && ! empty( $out_of_stock_string ) ) {
			?>
			<span class="product-out-of-stock"><em><?php echo esc_html( $out_of_stock_string ); ?></em></span>
			<?php
		}
	}
endif;

/**
 * Product loop image wrapper open
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_loop_product_image_wrapper_open', 4 );
function dmrthema_loop_product_image_wrapper_open() {
	echo '<div class="woocommerce-image__wrapper">';
}

/**
 * Product loop image wrapper close
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_loop_product_image_wrapper_close', 60 );
function dmrthema_loop_product_image_wrapper_close() {
	echo '</div>';
}

/**
 * Remove default WooCommerce product link open
 */
function dmrthema_remove_woocommerce_template_loop_product_link_open() {
	remove_action( 'woocommerce_before_shop_loop_item', 'woocommerce_template_loop_product_link_open', 10 );
}
add_action( 'wp_head', 'dmrthema_remove_woocommerce_template_loop_product_link_open' );

/**
 * Remove default WooCommerce product link close
 */
function dmrthema_remove_woocommerce_template_loop_product_link_close() {
	remove_action( 'woocommerce_after_shop_loop_item', 'woocommerce_template_loop_product_link_close', 5 );
}
add_action( 'wp_head', 'dmrthema_remove_woocommerce_template_loop_product_link_close' );

/**
 * Open link before the product thumbnail image
 */
function dmrthema_template_loop_image_link_open() {
	echo '<a href="' . get_the_permalink() . '" title="' . get_the_title() . '" class="woocommerce-LoopProduct-link woocommerce-loop-product__link">';
}

/**
 * Close link after the product thumbnail image
 */
function dmrthema_template_loop_image_link_close() {
	echo '</a>';
}

/**
 * Add link before product thumbnail
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_template_loop_image_link_open', 5 );

/**
 * Add link after product thumbnail
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_template_loop_image_link_close', 25 );

/**
 * Enqueue WooCommerce styles and scripts
 */
function dmrthema_woocommerce_scripts() {
	if ( is_woocommerce() || is_cart() || is_checkout() || is_account_page() ) {
		wp_enqueue_style( 'dmrthema-woocommerce', get_template_directory_uri() . '/assets/css/woocommerce.css', array(), '1.0.0' );
	}
	
	if ( is_product() ) {
		wp_enqueue_style( 'dmrthema-product', get_template_directory_uri() . '/assets/css/product.css', array(), '1.0.0' );
		wp_enqueue_script( 'dmrthema-product-js', get_template_directory_uri() . '/assets/js/product.js', array('jquery'), '1.0.0', true );
	}
}
add_action( 'wp_enqueue_scripts', 'dmrthema_woocommerce_scripts' );

/**
 * Add to cart message filter
 */
add_filter( 'wc_add_to_cart_message_html', 'dmrthema_add_to_cart_message_filter', 10, 2 );
function dmrthema_add_to_cart_message_filter( $message, $products ) {
	$titles = array();
	$count  = 0;

	if ( ! is_array( $products ) ) {
		$products = array( $products => 1 );
		$show_qty = false;
	}

	if ( ! $show_qty ) {
		$show_qty = false;
	}

	foreach ( $products as $product_id => $qty ) {
		$titles[] = ( $qty > 1 ? absint( $qty ) . ' &times; ' : '' ) . sprintf( _x( '&ldquo;%s&rdquo;', 'Item name in quotes', 'woocommerce' ), strip_tags( get_the_title( $product_id ) ) );
		$count   += $qty;
	}

	$titles     = array_filter( $titles );
	$added_text = sprintf( _n( '%s has been added to your cart.', '%s have been added to your cart.', $count, 'woocommerce' ), wc_format_list_of_items( $titles ) );

	$message = sprintf(
		'<div class="message-inner"><div class="message-content">%s</div><div class="buttons-wrapper"><a href="%s" tabindex="1" class="button wc-forward">%s</a><a href="%s" tabindex="1" class="button checkout wc-forward">%s</a></div></div>',
		esc_html( $added_text ),
		esc_url( wc_get_cart_url() ),
		esc_html__( 'View cart', 'woocommerce' ),
		esc_url( wc_get_checkout_url() ),
		esc_html__( 'Checkout', 'woocommerce' )
	);

	return $message;
}

/**
 * Sticky Add to Cart
 */
if ( ! function_exists( 'dmrthema_sticky_single_add_to_cart' ) ) {
	function dmrthema_sticky_single_add_to_cart() {
		$dmrthema_layout_woocommerce_sticky_cart_display = get_theme_mod( 'dmrthema_layout_woocommerce_sticky_cart_display', false );
		$dmrthema_layout_woocommerce_single_product_ajax = get_theme_mod( 'dmrthema_layout_woocommerce_single_product_ajax', false );

		global $woocommerce, $product;

		$id = $product->get_id();

		if ( true === $dmrthema_layout_woocommerce_sticky_cart_display ) {
			if ( $product->is_in_stock() ) {
				$dmrthema_sticky_addtocart_js  = '';
				$dmrthema_sticky_addtocart_js .= "
				var stickycontainer = document.getElementsByClassName('dmrthema-sticky-add-to-cart')[0];

				function add_class_on_scroll() {
				    stickycontainer.classList.add('visible');
				}

				function remove_class_on_scroll() {
				    stickycontainer.classList.remove('visible');
				}

				window.addEventListener('scroll', function(){
				    scrollpos = window.scrollY;

				    if(scrollpos > 150){
				        add_class_on_scroll();
				    }
				    else {
				        remove_class_on_scroll();
				    }
				});

				window.addEventListener('scroll', function(e) {
			    	if (window.innerHeight + window.pageYOffset === document.documentElement.offsetHeight) {
			      		remove_class_on_scroll();
			    	}
			  	});

			  	window.onscroll = function(e) {
				    if ((window.innerHeight + window.scrollY) >= document.body.scrollHeight) {
				        remove_class_on_scroll();
				    }
				};
				";

				wp_add_inline_script( 'dmrthema-product-js', $dmrthema_sticky_addtocart_js );
			}

			if ( $product->is_in_stock() ) {
				?>
				<section class="dmrthema-sticky-add-to-cart">
					<div class="col-full">
						<div class="dmrthema-sticky-add-to-cart__content">
							<?php echo wp_kses_post( woocommerce_get_product_thumbnail( 'woocommerce_gallery_thumbnail' ) ); ?>
							<div class="dmrthema-sticky-add-to-cart__content-product-info">
								<span class="dmrthema-sticky-add-to-cart__content-title"><?php the_title(); ?></span>
								<?php
									$count = $product->get_review_count();
								if ( $count && wc_review_ratings_enabled() ) {
									echo wc_get_rating_html( $product->get_average_rating() );
								}
								?>
							</div>

							<div class="dmrthema-sticky-add-to-cart__content-button">
								<span class="dmrthema-sticky-add-to-cart__content-price"><?php echo wp_kses_post( $product->get_price_html() ); ?></span>
								<?php if ( 'simple' === $product->get_type() ) { ?>
									<a href="<?php echo esc_url( $product->add_to_cart_url() ); ?>" class="button product_type_simple add_to_cart_button ajax_add_to_cart" data-product_id="<?php echo absint( $id ); ?>" data-product_sku="<?php echo esc_attr( $product->get_sku() ); ?>" aria-label="<?php echo esc_attr( $product->add_to_cart_description() ); ?>" rel="nofollow"><?php echo esc_html( $product->add_to_cart_text() ); ?></a>
								<?php } else { ?>
									<a href="#dmrthema-sticky-anchor" class="button dmrthema-sticky-cta"><?php esc_html_e( 'Select options', 'dmrthema' ); ?></a>
								<?php } ?>
							</div>
						</div>
					</div>
				</section>
				<?php
			}
		}
	}
}

/**
 * Shop page - Out of Stock
 */
if ( ! function_exists( 'dmrthema_shop_out_of_stock' ) ) :
	function dmrthema_shop_out_of_stock() {
		$out_of_stock        = get_post_meta( get_the_ID(), '_stock_status', true );
		$out_of_stock_string = apply_filters( 'dmrthema_shop_out_of_stock_string', __( 'Out of stock', 'dmrthema' ) );
		if ( 'outofstock' === $out_of_stock && ! empty( $out_of_stock_string ) ) {
			?>
			<span class="product-out-of-stock"><em><?php echo esc_html( $out_of_stock_string ); ?></em></span>
			<?php
		}
	}
endif;
